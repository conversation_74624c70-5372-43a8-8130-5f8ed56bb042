{"version": "1.0.0", "description": "Cursor AI 文件保护配置", "protected_files": [".cursorrules", "projectManagement/cursor.md", "projectManagement/project-flow.md", "projectManagement/cursor-setup-guide.md", "projectManagement/.cursor-protect.json", "projectManagement/.cursor-memory.json", "projectManagement/code-standards.md", "package.json", "package-lock.json", "tsconfig.json", "tsconfig.main.json", "tsconfig.node.json", "vite.config.ts", ".giti<PERSON>re", "README.md", "INSTALLATION.md", "NETWORK_CONNECTIVITY_GUIDE.md", "SUBSCRIPTION_FEATURES.md", "SYSTEM_ARCHITECTURE.md"], "protected_directories": [".git", "node_modules", "dist", "build", ".vscode", "release", "venv", "data/datasets", "data/models", "logs"], "critical_files": [".cursorrules", "projectManagement/cursor.md", "package.json", "tsconfig.json"], "deletion_rules": {"require_confirmation": true, "backup_before_delete": true, "log_all_deletions": true, "critical_files_require_double_confirmation": true}, "modification_rules": {"backup_before_major_changes": true, "use_incremental_edits": true, "preserve_file_structure": true, "maintain_code_style": true}, "safety_checks": {"check_file_existence_before_operation": true, "verify_file_permissions": true, "scan_for_duplicate_code": true, "validate_syntax_before_save": true}, "recovery_options": {"auto_backup_interval": "1_hour", "max_backup_versions": 10, "git_integration": true, "rollback_on_error": true}, "allowed_operations": {"read": ["*"], "create": ["**/*", "!node_modules/**", "!.git/**", "!dist/**", "!build/**"], "modify": ["**/*", "!node_modules/**", "!.git/**", "!dist/**", "!build/**"], "delete": ["temp/**", "cache/**", "*.tmp", "*.log", "*.bak"]}, "forbidden_operations": {"delete_without_confirmation": [".cursorrules", "projectManagement/cursor.md", "package.json", "*.config.*"], "overwrite_without_backup": ["**/*.js", "**/*.ts", "**/*.py", "**/*.java", "**/*.go", "**/*.rs", "**/*.cpp", "**/*.c"], "modify_structure": ["node_modules/**", ".git/**", "dist/**"]}, "error_prevention": {"duplicate_code_detection": true, "circular_dependency_check": true, "unused_import_warning": true, "naming_convention_check": true}, "logging": {"log_file": "logs/cursor-operations.log", "log_level": "INFO", "include_timestamps": true, "include_file_hashes": true}}