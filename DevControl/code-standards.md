# 项目代码规范和开发标准

## 🛡️ 文件操作安全规范

### 禁止操作
1. **严禁直接删除文件** - 必须先检查 `.cursor-protect.json` 保护列表
2. **禁止覆盖重要文件** - 关键配置文件需要备份后再修改
3. **禁止删除目录结构** - 不得破坏项目的基本目录结构
4. **禁止修改依赖文件** - 依赖目录（如 `node_modules`、`vendor`、`target` 等）、`.git` 等目录禁止修改

### 安全操作流程
1. **文件修改前**：
   - 检查文件是否在保护列表中
   - 确认修改的必要性和影响范围
   - 创建备份（如果是重要文件）

2. **文件删除前**：
   - 必须获得用户明确确认
   - 记录删除原因到开发日志
   - 检查是否有其他文件依赖此文件

3. **大规模修改前**：
   - 创建 Git 提交点
   - 通知用户修改范围
   - 准备回滚方案

## 💻 代码开发规范

### 代码复用原则
1. **避免重复代码**：
   - 开发前先搜索现有实现：`@codebase 搜索相似功能`
   - 提取公共函数到合适的共享目录
   - 使用组件化设计减少重复

2. **模块化设计**：
   - 每个模块职责单一
   - 避免循环依赖
   - 使用清晰的接口定义

3. **代码一致性**：
   - 遵循现有项目的命名规范
   - 保持代码风格统一
   - 使用相同的错误处理模式

### 文件修改规范
1. **增量修改优先**：
   ```markdown
   ✅ 推荐：使用 str_replace 进行局部修改
   ❌ 避免：重写整个文件
   ```

2. **保持文件结构**：
   ```markdown
   ✅ 推荐：在现有结构基础上添加功能
   ❌ 避免：重新组织整个文件结构
   ```

3. **注释和文档**：
   ```markdown
   ✅ 推荐：为新功能添加清晰注释
   ✅ 推荐：更新相关文档
   ❌ 避免：删除现有有用注释
   ```

### 错误处理标准
1. **异常处理**：
   ```
   // ✅ 推荐 - 使用适合项目语言的错误处理
   try {
     result = performOperation()
     return { success: true, data: result }
   } catch (error) {
     logError('Operation failed:', error)
     return { success: false, error: error.message }
   }

   // ❌ 避免 - 没有错误处理
   result = performOperation() // 可能抛出异常
   ```

2. **输入验证**：
   ```
   // ✅ 推荐 - 验证输入数据
   function processData(data) {
     if (!data || !isValidFormat(data)) {
       throw new Error('Invalid data format')
     }
     // 处理逻辑
   }
   ```

3. **错误日志**：
   ```
   // ✅ 推荐 - 记录详细错误信息
   logError(`[${timestamp}] Error in ${functionName}:`, error)
   ```

## 📋 项目管理规范

### 任务状态同步
1. **开始开发时**：
   - 将 `cursor.md` 中任务状态改为 `⏳ 进行中`
   - 记录开始时间
   - 确认任务依赖已完成

2. **开发过程中**：
   - 遇到问题及时记录到开发日志
   - 定期更新进度
   - 如需修改任务范围，先更新文档

3. **完成开发时**：
   - 将任务状态改为 `✅ 已完成`
   - 记录实际耗时
   - 更新阶段完成度
   - 提交代码并关联任务编号

### Git 提交规范
```bash
# 功能开发
git commit -m "feat: [任务编号] 实现核心业务功能"

# Bug 修复
git commit -m "fix: [任务编号] 修复数据处理问题"

# 文档更新
git commit -m "docs: [任务编号] 更新项目文档"

# 代码重构
git commit -m "refactor: [任务编号] 重构核心模块"

# 测试相关
git commit -m "test: [任务编号] 添加功能测试用例"
```

### 版本控制规范
1. **版本号规则**：
   - 主版本号：重大功能更新
   - 次版本号：新功能添加
   - 修订号：Bug 修复

2. **发布流程**：
   - 完成阶段性开发
   - 运行所有测试
   - 更新版本号和发布说明
   - 创建 Git 标签

## 🧪 测试和质量保证

### 测试要求
1. **单元测试**：
   - 新功能必须有对应测试
   - 测试覆盖率不低于 80%
   - 测试文件命名：根据项目测试框架确定（如 `*.test.*` 或 `*.spec.*`）

2. **集成测试**：
   - 关键功能需要集成测试
   - 关键接口必须有测试覆盖
   - 数据库操作需要测试验证

3. **代码审查**：
   - 重要功能需要代码审查
   - 检查代码规范和最佳实践
   - 验证错误处理和边界条件

### 性能标准
1. **响应时间**：
   - 接口响应时间合理
   - 用户界面响应及时
   - 数据处理优化

2. **资源使用**：
   - 内存使用合理
   - 避免内存泄漏
   - 优化大文件处理

## 🔍 代码审查检查清单

### 功能性检查
- [ ] 功能是否按需求实现
- [ ] 边界条件是否处理
- [ ] 错误情况是否考虑
- [ ] 性能是否满足要求

### 代码质量检查
- [ ] 命名是否清晰易懂
- [ ] 函数是否职责单一
- [ ] 是否有重复代码
- [ ] 注释是否充分

### 安全性检查
- [ ] 输入是否验证
- [ ] 敏感信息是否保护
- [ ] 权限控制是否正确
- [ ] SQL 注入等安全问题

### 可维护性检查
- [ ] 代码结构是否清晰
- [ ] 依赖关系是否合理
- [ ] 配置是否外部化
- [ ] 日志是否完善

## 🚨 紧急情况处理

### 代码回滚流程
1. **识别问题**：确认需要回滚的范围
2. **创建备份**：保存当前状态
3. **执行回滚**：使用 Git 回滚到稳定版本
4. **验证功能**：确认回滚后系统正常
5. **记录问题**：更新错误记忆系统

### 数据恢复流程
1. **停止相关服务**：防止数据进一步损坏
2. **评估损失**：确认数据损坏范围
3. **恢复备份**：使用最近的可用备份
4. **验证数据**：确认数据完整性
5. **重启服务**：恢复正常运行

## 📊 质量指标

### 代码质量指标
- 代码重复率 < 3%
- 测试覆盖率 > 80%
- 代码审查通过率 > 95%
- 静态分析无严重问题

### 开发效率指标
- 任务完成率 > 90%
- 时间预估准确率 > 80%
- Bug 修复时间 < 1天
- 功能交付及时率 > 85%

### 稳定性指标
- 系统可用性 > 99%
- 错误率 < 1%
- 性能回归次数 = 0
- 安全漏洞数量 = 0
