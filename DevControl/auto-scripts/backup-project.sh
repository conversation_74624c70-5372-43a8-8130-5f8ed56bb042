#!/bin/bash
# 项目自动备份脚本

PROJECT_NAME=$(basename "$(pwd)")
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
CONFIG_FILE="./DevControl/backup-config.json"

echo "🔄 开始备份项目管理系统: $PROJECT_NAME"

# 从配置文件读取 projectManagement 路径
if [ -f "$CONFIG_FILE" ]; then
    TEMPLATE_PATH=$(grep -o '"template_path": *"[^"]*"' "$CONFIG_FILE" | cut -d'"' -f4)
fi

# 如果配置文件中没有路径，尝试检测
if [ -z "$TEMPLATE_PATH" ]; then
    if [ -d "./projectManagement" ]; then
        TEMPLATE_PATH="./projectManagement"
    else
        echo "❌ 未找到 projectManagement 路径"
        echo "请确保已设置模板路径或 projectManagement 目录存在"
        exit 1
    fi
fi

# 验证 projectManagement 目录存在
if [ ! -d "$TEMPLATE_PATH" ]; then
    echo "❌ projectManagement 目录不存在: $TEMPLATE_PATH"
    exit 1
fi

# 确定备份目录位置（与 projectManagement 同级）
TEMPLATE_DIR=$(dirname "$TEMPLATE_PATH")
BACKUP_DIR="$TEMPLATE_DIR/backups/"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 创建备份目录名（使用真实项目名称）
BACKUP_NAME="${PROJECT_NAME}_backup_${TIMESTAMP}"
BACKUP_PATH="$BACKUP_DIR$BACKUP_NAME"

# 复制 projectManagement 目录到备份位置
cp -r "$TEMPLATE_PATH" "$BACKUP_PATH"

echo "✅ 备份完成: $BACKUP_PATH"
echo "📁 备份位置: $(cd "$BACKUP_DIR" && pwd)/$BACKUP_NAME"

# 记录备份信息
echo "$(date): 备份创建 - $BACKUP_NAME (项目: $PROJECT_NAME, 源路径: $TEMPLATE_PATH)" >> ./DevControl/logs/backup.log

# 清理旧备份（保留最新10个，按项目名称匹配）
cd "$BACKUP_DIR"
ls -t | grep "${PROJECT_NAME}_backup_" | tail -n +11 | xargs -r rm -rf
cd - > /dev/null
