#!/bin/bash
# 项目健康检查脚本

echo "🔍 项目健康检查"
echo "================"

# 检查必要文件
echo "📋 检查必要文件:"
required_files=("DevControl/project-status.md" "DevControl/task-management.md" "DevControl/development-log.md")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (缺失)"
    fi
done

# 检查项目结构
echo ""
echo "📁 项目结构检查:"
common_dirs=("src" "lib" "app" "main" "source" "code")
found_main_dir=false
for dir in "${common_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "  ✅ $dir/ 目录存在"
        found_main_dir=true
        break
    fi
done
if [ "$found_main_dir" = false ]; then
    echo "  ⚠️  未找到常见的主代码目录"
fi

# 检查配置文件
echo ""
echo "⚙️ 配置文件检查:"
config_files=("package.json" "requirements.txt" "pom.xml" "Cargo.toml" "go.mod" ".gitignore" "README.md")
for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    fi
done

echo ""
echo "✅ 健康检查完成"
