{"context_awareness": {"enabled": true, "auto_detect_location": true, "show_context_banner": true, "track_modification_scope": true}, "project_structure": {"auto_map_generation": true, "module_detection_patterns": ["src/**/", "lib/**/", "app/**/", "components/**/", "services/**/", "utils/**/", "models/**/", "controllers/**/", "views/**/"], "ignore_patterns": ["node_modules/**", ".git/**", "dist/**", "build/**", "*.log", "*.tmp"]}, "context_tracking": {"current_module": "", "current_feature": "", "current_files": [], "modification_scope": "", "related_dependencies": [], "impact_analysis": {"upstream_modules": [], "downstream_modules": [], "interface_changes": []}}, "navigation_commands": {"@where": {"description": "显示当前工作位置和上下文", "response_template": "🔍 当前位置：\n📁 模块: {current_module}\n📄 文件: {current_file}\n🎯 功能: {current_feature}\n🔗 依赖: {dependencies}"}, "@map": {"description": "显示项目架构地图", "response_template": "🗺️ 项目架构：\n{project_structure_tree}\n\n📊 模块状态：\n{module_status_table}"}, "@deps": {"description": "显示当前模块的依赖关系", "response_template": "🔗 依赖关系：\n⬆️ 上游依赖: {upstream_deps}\n⬇️ 下游影响: {downstream_impact}\n🔄 接口关系: {interfaces}"}, "@impact": {"description": "分析修改的影响范围", "response_template": "📊 影响分析：\n🎯 修改范围: {modification_scope}\n⚠️ 影响模块: {affected_modules}\n🔧 需要更新: {update_requirements}"}, "@focus": {"description": "切换到指定模块的上下文", "parameters": ["module_name"], "response_template": "🎯 切换到模块: {module_name}\n📋 模块信息：\n- 职责: {module_responsibility}\n- 核心文件: {core_files}\n- 当前状态: {module_status}"}, "@task": {"description": "切换到指定任务的上下文", "parameters": ["task_id"], "response_template": "📋 切换到任务: {task_id}\n🎯 任务信息：\n- 功能: {task_feature}\n- 涉及模块: {involved_modules}\n- 修改文件: {target_files}"}}, "context_reminders": {"before_modification": {"enabled": true, "template": "⚠️ 修改提醒：\n📁 当前模块: {current_module}\n🎯 功能范围: {feature_scope}\n📊 影响评估: {impact_level}\n✅ 确认继续修改？"}, "during_modification": {"enabled": true, "show_context_banner": true, "banner_template": "🧭 [{current_module}] {current_feature} | 📄 {current_file}"}, "after_modification": {"enabled": true, "template": "✅ 修改完成：\n📝 更新内容: {modification_summary}\n🔄 需要同步: {sync_requirements}\n📋 下一步: {next_steps}"}}, "auto_context_detection": {"file_patterns": {"controller": {"patterns": ["*Controller.*", "*controller.*", "controllers/**"], "module_type": "控制层", "typical_functions": ["请求处理", "业务逻辑调度", "响应生成"]}, "model": {"patterns": ["*Model.*", "*model.*", "models/**", "*entity.*"], "module_type": "数据层", "typical_functions": ["数据模型", "数据验证", "数据库操作"]}, "view": {"patterns": ["*View.*", "*view.*", "views/**", "templates/**"], "module_type": "视图层", "typical_functions": ["界面渲染", "用户交互", "数据展示"]}, "service": {"patterns": ["*Service.*", "*service.*", "services/**"], "module_type": "服务层", "typical_functions": ["业务逻辑", "外部接口", "数据处理"]}, "util": {"patterns": ["*Util.*", "*util.*", "utils/**", "helpers/**"], "module_type": "工具层", "typical_functions": ["通用工具", "辅助函数", "公共方法"]}, "config": {"patterns": ["*config.*", "*.config.*", "config/**", "*.env*"], "module_type": "配置层", "typical_functions": ["系统配置", "环境变量", "参数设置"]}}}, "smart_suggestions": {"enabled": true, "suggestion_types": {"related_files": "基于当前修改建议相关文件", "dependency_check": "检查依赖关系是否需要更新", "interface_validation": "验证接口变更的兼容性", "test_requirements": "建议需要更新的测试文件"}}, "integration_settings": {"update_task_management": true, "update_development_log": true, "auto_backup_on_module_change": true, "sync_with_project_status": true}}