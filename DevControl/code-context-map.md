# 代码上下文地图模板

## 🗺️ 项目架构总览

### 项目结构图
```
项目名称/
├── 📁 [主要模块1]/
│   ├── 📄 [核心文件1] - [功能描述]
│   ├── 📄 [核心文件2] - [功能描述]
│   └── 📁 [子模块]/
│       └── 📄 [文件] - [功能描述]
├── 📁 [主要模块2]/
│   ├── 📄 [核心文件1] - [功能描述]
│   └── 📄 [核心文件2] - [功能描述]
└── 📁 [配置目录]/
    ├── 📄 [配置文件1] - [配置说明]
    └── 📄 [配置文件2] - [配置说明]
```

### 模块职责矩阵
| 模块名称 | 主要职责 | 核心文件 | 依赖关系 | 状态 |
|---------|---------|---------|---------|------|
| [模块1] | [职责描述] | [文件列表] | [依赖模块] | 🟢 稳定 |
| [模块2] | [职责描述] | [文件列表] | [依赖模块] | 🟡 开发中 |
| [模块3] | [职责描述] | [文件列表] | [依赖模块] | 🔴 待开发 |

## 🎯 当前工作上下文

### 正在开发的功能
- **功能名称**: [当前开发的具体功能]
- **所属模块**: [模块名称]
- **涉及文件**: [文件路径列表]
- **功能范围**: [详细描述功能边界]
- **相关任务**: [关联的任务编号]

### 当前位置标记
```
🔍 当前工作位置：
📁 目录: [当前工作目录]
📄 文件: [当前编辑的文件]
🎯 功能: [正在实现的具体功能]
🔗 关联: [相关的其他文件或模块]
```

### 上下文依赖
- **上游依赖**: [此功能依赖的其他模块/文件]
- **下游影响**: [此功能会影响的其他模块/文件]
- **数据流向**: [数据如何在模块间流动]
- **接口关系**: [与其他模块的接口定义]

## 📋 代码修改指南

### 修改前检查清单
- [ ] 确认当前工作的模块和功能范围
- [ ] 检查相关文件的依赖关系
- [ ] 确认修改不会破坏现有接口
- [ ] 验证修改符合模块的职责边界

### 修改时的上下文提醒
```
⚠️ 修改提醒：
- 当前模块: [模块名]
- 功能范围: [功能描述]
- 影响范围: [可能影响的其他部分]
- 注意事项: [特殊注意事项]
```

### 修改后的更新
- [ ] 更新模块状态
- [ ] 记录接口变更
- [ ] 更新依赖关系
- [ ] 通知相关模块维护者

## 🔄 模块间协作规范

### 接口定义
| 模块A | 模块B | 接口类型 | 接口描述 | 数据格式 |
|-------|-------|---------|---------|---------|
| [模块1] | [模块2] | [API/函数/事件] | [接口说明] | [数据结构] |

### 数据流图
```
[数据源] → [处理模块1] → [处理模块2] → [输出目标]
    ↓           ↓           ↓
[存储层]   [缓存层]   [日志系统]
```

### 调用关系
```
调用链路：
[入口模块] 
  ↓ 调用
[业务模块A]
  ↓ 调用  
[业务模块B]
  ↓ 调用
[数据层模块]
```

## 🎯 开发导航系统

### 功能开发路径
1. **需求分析** → 确定涉及的模块
2. **架构设计** → 确定修改的文件范围
3. **接口设计** → 确定模块间的交互
4. **代码实现** → 按模块逐步实现
5. **集成测试** → 验证模块间协作

### 代码定位助手
```
🧭 代码导航：
当前任务: [任务描述]
  ↓
涉及模块: [模块列表]
  ↓  
核心文件: [文件路径]
  ↓
具体函数: [函数/类名]
  ↓
修改范围: [代码行数范围]
```

## 📊 开发进度追踪

### 模块开发状态
- **[模块1]**: 
  - 进度: 80%
  - 当前任务: [具体任务]
  - 下一步: [下一步计划]
  
- **[模块2]**: 
  - 进度: 30%
  - 当前任务: [具体任务]
  - 阻塞问题: [问题描述]

### 文件修改历史
| 日期 | 文件路径 | 修改类型 | 功能描述 | 影响范围 |
|------|---------|---------|---------|---------|
| [日期] | [路径] | [新增/修改/删除] | [功能说明] | [影响说明] |

## 🔍 上下文查询命令

### 快速定位命令
- `@where` - 显示当前工作位置和上下文
- `@map` - 显示项目架构地图
- `@deps` - 显示当前模块的依赖关系
- `@impact` - 分析修改的影响范围
- `@path` - 显示功能实现路径

### 上下文切换命令
- `@focus [模块名]` - 切换到指定模块的上下文
- `@task [任务ID]` - 切换到指定任务的上下文
- `@file [文件路径]` - 切换到指定文件的上下文

## ⚠️ 重要提醒

### 开发原则
1. **单一职责**: 每次只专注一个模块的一个功能
2. **明确边界**: 清楚知道修改的范围和影响
3. **保持上下文**: 始终知道自己在项目中的位置
4. **记录变更**: 及时更新上下文地图

### 避免的陷阱
- ❌ 跨模块随意修改
- ❌ 不明确功能边界
- ❌ 忽略依赖关系
- ❌ 缺乏整体视角

这个上下文地图将帮助 AI 始终保持清晰的方向感，避免在复杂项目中迷失！
