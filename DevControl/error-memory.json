{"version": "1.0.0", "description": "Cursor AI 错误记忆和学习系统", "last_updated": "2024-01-01", "common_mistakes": [{"id": "FILE_DELETE_001", "error": "删除重要配置文件", "description": "AI 尝试删除 .cursorrules 或 package.json 等关键文件", "solution": "始终检查 .cursor-protect.json 中的保护列表", "prevention": "在删除任何文件前询问用户确认并检查保护列表", "severity": "HIGH", "frequency": 0, "last_occurred": null}, {"id": "CODE_DUPLICATE_001", "error": "重复创建相同功能的代码", "description": "在不同文件中创建了相同功能的函数或组件", "solution": "先检查现有代码库，使用 @codebase 搜索相似功能", "prevention": "每次编码前先搜索现有实现，使用代码复用", "severity": "MEDIUM", "frequency": 0, "last_occurred": null}, {"id": "FILE_OVERWRITE_001", "error": "覆盖现有文件而不备份", "description": "直接重写整个文件而不是增量修改", "solution": "使用 str_replace 进行增量编辑", "prevention": "修改现有文件时使用 str_replace 而不是 save_file", "severity": "HIGH", "frequency": 0, "last_occurred": null}, {"id": "DEPENDENCY_CONFLICT_001", "error": "创建循环依赖", "description": "在模块间创建了循环引用", "solution": "重构代码结构，提取公共依赖", "prevention": "设计模块时检查依赖关系图", "severity": "MEDIUM", "frequency": 0, "last_occurred": null}, {"id": "CONFIG_INCONSISTENT_001", "error": "配置文件不一致", "description": "修改了一个配置文件但没有同步其他相关配置", "solution": "检查所有相关配置文件并保持一致性", "prevention": "修改配置时列出所有相关文件并逐一检查", "severity": "MEDIUM", "frequency": 0, "last_occurred": null}], "code_patterns": {"avoid": [{"pattern": "直接删除文件", "reason": "可能删除重要文件", "alternative": "先检查保护列表，询问用户确认"}, {"pattern": "重复的函数定义", "reason": "增加维护成本", "alternative": "搜索现有实现，使用代码复用"}, {"pattern": "硬编码的配置值", "reason": "降低代码灵活性", "alternative": "使用配置文件或环境变量"}, {"pattern": "未处理的异常", "reason": "可能导致程序崩溃", "alternative": "添加 try-catch 和错误处理"}, {"pattern": "全局变量污染", "reason": "影响代码可维护性", "alternative": "使用模块化设计和局部作用域"}], "prefer": [{"pattern": "增量式修改", "reason": "保持代码历史和稳定性", "example": "使用 str_replace 而不是重写整个文件"}, {"pattern": "配置文件管理", "reason": "提高代码灵活性", "example": "将配置项放在 config.json 中"}, {"pattern": "错误处理机制", "reason": "提高程序健壮性", "example": "每个函数都有错误处理和返回值检查"}, {"pattern": "代码复用", "reason": "减少重复代码", "example": "提取公共函数到 utils 模块"}, {"pattern": "类型安全", "reason": "减少运行时错误", "example": "使用 TypeScript 类型定义"}]}, "successful_patterns": [{"pattern": "任务驱动开发", "description": "根据 cursor.md 中的任务状态进行开发", "success_rate": "95%", "benefits": ["避免重复工作", "保持开发进度", "提高效率"]}, {"pattern": "增量式代码修改", "description": "使用 str_replace 进行小范围修改", "success_rate": "90%", "benefits": ["保持代码稳定性", "易于回滚", "减少错误"]}, {"pattern": "文档同步更新", "description": "代码修改时同步更新相关文档", "success_rate": "85%", "benefits": ["保持文档准确性", "便于团队协作", "减少沟通成本"]}], "learning_rules": [{"rule": "每次错误后更新记忆", "description": "发生错误时自动记录到 common_mistakes 中", "implementation": "增加 frequency 计数，更新 last_occurred 时间"}, {"rule": "模式识别和预防", "description": "识别重复错误模式并主动预防", "implementation": "检查当前操作是否匹配已知错误模式"}, {"rule": "成功模式强化", "description": "记录和强化成功的开发模式", "implementation": "增加 successful_patterns 的使用频率"}], "context_awareness": {"project_type": "electron-react-app", "tech_stack": ["TypeScript", "React", "Electron", "Vite", "Python"], "coding_style": "functional-programming-preferred", "file_structure": "modular-architecture", "testing_framework": "jest", "build_tool": "vite"}, "performance_metrics": {"total_operations": 0, "successful_operations": 0, "failed_operations": 0, "prevented_errors": 0, "success_rate": "0%", "error_prevention_rate": "0%"}, "auto_learning": {"enabled": true, "learning_threshold": 3, "pattern_recognition": true, "adaptive_prevention": true}}