# Cursor AI 项目管理使用指南

## 🚀 快速开始

### 1. 一键设置保护机制
```bash
# 给脚本执行权限
chmod +x setup-cursor-protection.sh

# 运行设置脚本
./setup-cursor-protection.sh
```

### 2. 验证设置是否成功
```bash
# 检查项目状态
./check-cursor-status.sh
```

### 3. 在 Cursor 中测试 AI 助手
打开 Cursor，在聊天窗口输入：
```
@cursor 请检查项目配置文件是否完整，并显示当前项目状态
```

## 📋 日常使用流程

### 每次打开 Cursor 时
1. **运行状态检查**：
   ```bash
   ./check-cursor-status.sh
   ```

2. **在 Cursor 中确认 AI 配置**：
   ```
   @cursor 根据 cursor.md 显示当前应该开发的任务
   ```

### 开始新功能开发
1. **检查任务状态**：
   ```
   @cursor 我要开发 [功能名称]，请先检查：
   1. cursor.md 中的任务状态
   2. 是否有类似功能已存在
   3. 确认开发计划
   ```

2. **生成代码**：
   ```
   @cursor 请为 [功能名称] 生成代码，要求：
   1. 遵循项目代码规范
   2. 使用增量修改方式
   3. 添加必要的测试
   4. 更新相关文档
   ```

3. **完成开发**：
   ```
   @cursor 功能开发完成，请帮我：
   1. 更新 cursor.md 中的任务状态
   2. 记录实际开发时间
   3. 提供 Git 提交建议
   ```

### 修改现有代码
1. **安全修改**：
   ```
   @cursor 我要修改 [文件名]，请：
   1. 先检查文件是否受保护
   2. 使用 str_replace 进行增量修改
   3. 保持代码风格一致
   4. 备份重要更改
   ```

### 删除文件或代码
1. **安全删除**：
   ```
   @cursor 我需要删除 [文件/代码]，请：
   1. 检查保护列表
   2. 确认删除影响范围
   3. 创建备份
   4. 获得我的明确确认后再执行
   ```

## 🛡️ 安全机制说明

### 文件保护系统
- **保护文件列表**：`.cursor-protect.json` 中定义
- **自动检查**：AI 在操作前会自动检查
- **用户确认**：重要操作需要用户明确确认

### 错误记忆系统
- **错误记录**：`.cursor-memory.json` 中存储常见错误
- **模式识别**：AI 会识别并避免重复错误
- **学习改进**：系统会不断学习和改进

### 代码质量保证
- **重复检测**：自动检查是否有重复代码
- **规范遵循**：严格按照 `code-standards.md` 执行
- **增量修改**：优先使用 `str_replace` 而不是重写

## 🎯 常用命令模板

### 项目管理命令
```
# 检查项目进度
@cursor 显示项目整体进度和下一步计划

# 更新任务状态
@cursor 将任务 [任务名] 状态更新为已完成，记录耗时 [X] 天

# 生成进度报告
@cursor 生成本周开发进度报告
```

### 代码开发命令
```
# 搜索现有功能
@cursor 搜索项目中是否已有 [功能描述] 的实现

# 生成新功能
@cursor 为 [功能名] 生成代码，包括主要逻辑和测试用例

# 重构代码
@cursor 重构 [文件名] 中的 [函数名]，提高代码质量
```

### 文档维护命令
```
# 更新文档
@cursor 根据最新代码更新 README.md 文档

# 生成API文档
@cursor 为新增的API接口生成文档

# 更新变更日志
@cursor 更新 CHANGELOG.md，记录本次版本的主要变更
```

## 🔧 故障排除

### AI 不遵循保护规则
1. **检查配置文件**：确认 `.cursorrules` 文件完整
2. **重启 Cursor**：重新加载配置
3. **手动提醒**：在命令中明确提及保护规则

### 文件被意外删除
1. **Git 恢复**：`git checkout HEAD -- [文件名]`
2. **备份恢复**：从备份目录恢复
3. **更新保护列表**：将文件添加到保护列表

### 代码重复创建
1. **搜索现有代码**：使用 `@codebase` 搜索
2. **合并重复代码**：保留最优版本
3. **更新记忆系统**：记录到错误记忆中

### AI 响应异常
1. **清除聊天历史**：开始新的对话
2. **检查网络连接**：确保网络正常
3. **重启应用**：重新启动 Cursor

## 📊 效果监控

### 每日检查指标
- [ ] 任务完成情况
- [ ] 代码质量状况
- [ ] 错误发生次数
- [ ] 保护机制有效性

### 每周评估指标
- [ ] 开发效率提升
- [ ] 错误减少程度
- [ ] 代码重复率
- [ ] 文档更新及时性

### 每月优化建议
- [ ] 更新保护规则
- [ ] 完善错误记忆
- [ ] 调整工作流程
- [ ] 培训团队成员

## 🆘 紧急联系

### 遇到严重问题时
1. **立即停止操作**：避免进一步损失
2. **备份当前状态**：`./backup-project.sh`
3. **记录问题详情**：截图和错误信息
4. **寻求帮助**：联系技术支持

### 数据恢复流程
1. **评估损失范围**
2. **选择恢复方案**
3. **执行恢复操作**
4. **验证恢复结果**
5. **更新保护机制**

## 📚 进阶使用

### 自定义保护规则
编辑 `.cursor-protect.json` 添加项目特定的保护规则

### 扩展错误记忆
在 `.cursor-memory.json` 中添加项目特有的错误模式

### 优化工作流程
根据团队需求调整 `cursor.md` 中的任务模板

### 集成外部工具
配置与 Git、CI/CD 等工具的集成

---

## 🎉 恭喜！

您已经掌握了 Cursor AI 项目管理系统的使用方法。这套系统将帮助您：

- ✅ 避免常见的开发错误
- ✅ 提高代码质量和一致性
- ✅ 加速项目开发进度
- ✅ 保护重要项目文件
- ✅ 建立可持续的开发流程

祝您开发愉快！🚀
