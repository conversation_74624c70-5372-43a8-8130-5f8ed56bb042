directories:
  output: dist
  buildResources: build
appId: com.coovally.desktop
productName: Coovally AI Platform
artifactName: ${productName}-${version}-${os}-${arch}.${ext}
files:
  - filter:
      - src/**/*
      - web-app/**/*
      - node_modules/**/*
      - package.json
      - '!node_modules/.cache/**/*'
      - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
      - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
      - '!**/node_modules/*.d.ts'
      - '!**/node_modules/.bin'
      - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
      - '!.editorconfig'
      - '!**/._*'
      - '!**/{.DS_Store,.git,.hg,.svn,<PERSON><PERSON>,RC<PERSON>,SCCS,.gitignore,.gitattributes}'
      - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}'
      - '!**/{appveyor.yml,.travis.yml,circle.yml}'
      - '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
extraResources:
  - from: assets/
    to: assets/
    filter:
      - '**/*'
mac:
  target:
    target: zip
    arch:
      - x64
      - arm64
  icon: build/icon.icns
  category: public.app-category.graphics-design
  entitlements: build/entitlements.mac.plist
  hardenedRuntime: false
  gatekeeperAssess: false
win:
  icon: build/icon.ico
  publisherName: Coovally Team
  verifyUpdateCodeSignature: false
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
    - target: zip
      arch:
        - x64
        - ia32
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
  installerHeaderIcon: build/icon.ico
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: ${productName}
  include: build/installer.nsh
portable:
  artifactName: ${productName}-${version}-portable.${ext}
linux:
  icon: build/icon.png
  category: Development
  maintainer: Coovally Team
  vendor: Coovally
  synopsis: Coovally AI Platform Desktop Application
  description: A powerful AI platform desktop application for data science and machine learning
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
    - target: rpm
      arch:
        - x64
    - target: tar.xz
      arch:
        - x64
appImage:
  artifactName: ${productName}-${version}.${ext}
deb:
  priority: optional
  depends:
    - libgtk-3-0
    - libnotify4
    - libnss3
    - libxss1
    - libxtst6
    - xdg-utils
    - libatspi2.0-0
    - libdrm2
    - libxcomposite1
    - libxdamage1
    - libxrandr2
    - libgbm1
    - libxkbcommon0
    - libasound2
rpm:
  depends:
    - gtk3
    - libnotify
    - nss
    - libXScrnSaver
    - libXtst
    - xdg-utils
    - at-spi2-atk
    - libdrm
    - libXcomposite
    - libXdamage
    - libXrandr
    - mesa-libgbm
    - libxkbcommon
    - alsa-lib
publish:
  provider: github
  owner: coovally
  repo: coovally-desktop
electronVersion: 13.6.9
