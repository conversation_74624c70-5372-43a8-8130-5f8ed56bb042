<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0"><dict><key>AsarIntegrity</key><string>{&#34;checksums&#34;:{&#34;app.asar&#34;:&#34;Q0ihk/++bnUSEXe0L9pSgSVZ1NDBzWvTpfxQnkQYYswvGM77HrPpSdoRQ2uQFaafwZm26efmU6k5ykREuclrCg==&#34;}}</string><key>CFBundleDisplayName</key><string>Coovally AI Platform</string><key>CFBundleExecutable</key><string>Coovally AI Platform</string><key>CFBundleIconFile</key><string>electron.icns</string><key>CFBundleIdentifier</key><string>com.coovally.desktop</string><key>CFBundleInfoDictionaryVersion</key><string>6.0</string><key>CFBundleName</key><string>Coovally AI Platform</string><key>CFBundlePackageType</key><string>APPL</string><key>CFBundleShortVersionString</key><string>1.0.0</string><key>CFBundleVersion</key><string>1.0.0</string><key>DTCompiler</key><string>com.apple.compilers.llvm.clang.1_0</string><key>DTSDKBuild</key><string>11.1</string><key>DTSDKName</key><string>macosx11.1</string><key>DTXcode</key><string>1240</string><key>DTXcodeBuild</key><string>12D4e</string><key>LSApplicationCategoryType</key><string>public.app-category.graphics-design</string><key>LSMinimumSystemVersion</key><string>10.11.0</string><key>NSAppTransportSecurity</key><dict><key>NSAllowsArbitraryLoads</key><true/><key>NSAllowsLocalNetworking</key><true/><key>NSExceptionDomains</key><dict><key>127.0.0.1</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict><key>localhost</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict></dict></dict><key>NSBluetoothAlwaysUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSBluetoothPeripheralUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSCameraUsageDescription</key><string>This app needs access to the camera</string><key>NSHighResolutionCapable</key><true/><key>NSHumanReadableCopyright</key><string>Copyright © 2025 Coovally Team</string><key>NSMainNibFile</key><string>MainMenu</string><key>NSMicrophoneUsageDescription</key><string>This app needs access to the microphone</string><key>NSPrincipalClass</key><string>AtomApplication</string><key>NSQuitAlwaysKeepsWindows</key><false/><key>NSRequiresAquaSystemAppearance</key><false/><key>NSSupportsAutomaticGraphicsSwitching</key><true/></dict></plist>