; 自定义安装程序配置
; 安装时的额外操作
!macro customInstall
  ; 创建防火墙规则
  ExecWait 'netsh advfirewall firewall add rule name="Coovally Desktop" dir=in action=allow program="$INSTDIR\${APP_EXECUTABLE_FILENAME}" enable=yes'
  
  ; 注册协议处理器
  WriteRegStr HKCR "coovally" "" "URL:Coovally Protocol"
  WriteRegStr HKCR "coovally" "URL Protocol" ""
  WriteRegStr HKCR "coovally\DefaultIcon" "" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  WriteRegStr HKCR "coovally\shell\open\command" "" "$INSTDIR\${APP_EXECUTABLE_FILENAME} %1"
  
  ; 创建桌面快捷方式
  CreateShortCut "$DESKTOP\Coovally AI Platform.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  
  ; 创建开始菜单快捷方式
  CreateDirectory "$SMPROGRAMS\Coovally"
  CreateShortCut "$SMPROGRAMS\Coovally\Coovally AI Platform.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  CreateShortCut "$SMPROGRAMS\Coovally\卸载 Coovally.lnk" "$INSTDIR\Uninstall ${PRODUCT_NAME}.exe"
!macroend

; 卸载时的额外操作
!macro customUnInstall
  ; 删除防火墙规则
  ExecWait 'netsh advfirewall firewall delete rule name="Coovally Desktop"'
  
  ; 删除注册表项
  DeleteRegKey HKCR "coovally"
  
  ; 删除快捷方式
  Delete "$DESKTOP\Coovally AI Platform.lnk"
  Delete "$SMPROGRAMS\Coovally\Coovally AI Platform.lnk"
  Delete "$SMPROGRAMS\Coovally\卸载 Coovally.lnk"
  RMDir "$SMPROGRAMS\Coovally"
!macroend

; 安装程序页面自定义
!macro customHeader
  ; 自定义安装程序标题
  !define MUI_ICON "${BUILD_RESOURCES_DIR}\icon.ico"
  !define MUI_UNICON "${BUILD_RESOURCES_DIR}\icon.ico"
  !define MUI_HEADERIMAGE
  !define MUI_HEADERIMAGE_BITMAP "${BUILD_RESOURCES_DIR}\header.bmp"
  !define MUI_WELCOMEFINISHPAGE_BITMAP "${BUILD_RESOURCES_DIR}\welcome.bmp"
  !define MUI_UNWELCOMEFINISHPAGE_BITMAP "${BUILD_RESOURCES_DIR}\welcome.bmp"
!macroend 