
        /* Electron桌面应用样式优化 */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        .el-table[data-v-13a12d32] .el-table__body-wrapper::-webkit-scrollbar{width:1px;height:6px;border-radius:3px}.el-table[data-v-13a12d32] .el-table__body-wrapper::-webkit-scrollbar-thumb{width:1px;height:6px;background:rgba(92,105,123,.4);border-radius:3px}.Datalist[data-v-13a12d32]{-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.Datalist .content[data-v-13a12d32],.Datalist[data-v-13a12d32]{width:100%;display:-webkit-box;display:-ms-flexbox;display:flex}.Datalist .content[data-v-13a12d32]{height:100%}.Datalist .content .left_bar[data-v-13a12d32]{width:12%;min-width:203px}.Datalist .content .main[data-v-13a12d32]{max-width:88%;width:100%;height:calc(100% - 32px);overflow-x:hidden;margin-top:40px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;padding:0 56px}.Datalist .content .main header[data-v-13a12d32]{-webkit-box-flex:0;-ms-flex:0;flex:0;display:-webkit-box;display:-ms-flexbox;display:flex;margin-bottom:24px;height:40px;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.Datalist .content .main header[data-v-13a12d32] .el-input-group__append{background:#4568ee;color:#fff}.Datalist .content .main header[data-v-13a12d32] .el-input-group__append:hover{opacity:.8}.Datalist .content .main header .radio[data-v-13a12d32]{min-width:305px}.Datalist .content .main header .radio[data-v-13a12d32] .el-radio-button__inner{background:none;border:none;-webkit-box-shadow:none;box-shadow:none;padding:0;font-size:16px}.Datalist .content .main header .radio[data-v-13a12d32] .el-radio-button:first-child .el-radio-button__inner{border-radius:0}.Datalist .content .main header .radio[data-v-13a12d32] .el-radio-button{font-size:16px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#727a89;line-height:22px}.Datalist .content .main header .radio[data-v-13a12d32] .el-radio-button__inner:hover{color:#727a89!important}.Datalist .content .main header .radio[data-v-13a12d32] .is-active{-webkit-box-shadow:none;box-shadow:none}.Datalist .content .main header .radio[data-v-13a12d32] .is-active span{font-size:16px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:#252631!important;line-height:22px}.Datalist .content .main header .radio[data-v-13a12d32] .is-active:after{content:"";width:auto;height:1px;display:block;margin:0 auto;border-bottom:3px solid #4568ee;border-radius:2px;padding-top:3px}.Datalist .content .main header .inputSelect[data-v-13a12d32]{width:331px;margin-right:20px}.Datalist .content .main header #MdmTitlebutton[data-v-13a12d32]{border:none;height:40px;font-size:14px;border-radius:4px;background:#4568ee}.Datalist .content .main header #MdmTitlebutton[data-v-13a12d32]:hover{opacity:.8}.Datalist .content .main header #MdmTitlebutton #jia[data-v-13a12d32]{font-size:13px}.Datalist .content .main section[data-v-13a12d32]{width:100%}.Datalist .content .main section .project_content[data-v-13a12d32]{background:#fff;-webkit-box-sizing:border-box;box-sizing:border-box;padding:16px}.Datalist .content .main section .project_content .table[data-v-13a12d32]{width:100%}.Datalist .content .main section .project_content .table .delete_icon[data-v-13a12d32]{border:none;padding:5px;margin-left:16px;background:#f4f6fc}.Datalist .content .main section .project_content .table .unzipfail[data-v-13a12d32]{border:none;padding:5px;margin-right:20px;background:#f4f6fc}.Datalist .content .main section .project_content .table .name a.name_inner[data-v-13a12d32]{color:#252631;padding-right:10px;text-decoration:none}.Datalist .content .main section .project_content .table .name a.name_inner[data-v-13a12d32]:hover{color:#4568ee;text-decoration:underline;cursor:pointer}.Datalist .content .main section .project_content .table .name .disableName[data-v-13a12d32]{pointer-events:none}.Datalist .content .main section .project_content .table .soltcontent[data-v-13a12d32]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.Datalist .content .main section .project_content .table .soltcontent .wait[data-v-13a12d32]{width:8px;height:8px;background:#faad14;border-radius:50%;margin-right:6px}.Datalist .content .main section .project_content .table .soltcontent .Inexecution[data-v-13a12d32]{width:8px;height:8px;background:#4568ee;border-radius:50%;margin-right:6px}.Datalist .content .main section .project_content .table .soltcontent .stop[data-v-13a12d32]{width:8px;height:8px;background:#b8c1d2;border-radius:50%;margin-right:6px}.Datalist .content .main section .project_content .table .soltcontent .success[data-v-13a12d32]{width:8px;height:8px;background:#22c053;border-radius:50%;margin-right:6px}.Datalist .content .main section .project_content .table .soltcontent .fail[data-v-13a12d32]{width:8px;height:8px;background:#f44848;border-radius:50%;margin-right:6px}.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .datadesctitle[data-v-13a12d32],.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .datanametitle[data-v-13a12d32],.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .fileupload[data-v-13a12d32],.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .picturesuffixtitle[data-v-13a12d32]{height:20px;font-size:14px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:#252631;line-height:20px;margin-top:24px;margin-bottom:16px}.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .upload_space[data-v-13a12d32]{height:259px;background:#f6f7f9;border-radius:4px;border:1px dashed #9aa1ab;text-align:center;position:relative}.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .upload_space .el-progress[data-v-13a12d32]{width:100%;position:absolute;bottom:50px;z-index:2001}.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .upload_space .ui_text[data-v-13a12d32]{font-size:16px;line-height:22px;padding:83px 0 32px 0}.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .upload_space .ui_btn[data-v-13a12d32]{overflow:hidden;display:inline-block;border-radius:20px;border:0;padding:9px 32px;text-decoration:none;text-indent:0;line-height:20px;background:#00f;font-size:16px;font-family:PingFangSC-Semibold,PingFang SC;font-weight:600;color:#fff;z-index:2}.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .upload_space .ui_btn input[data-v-13a12d32]{position:absolute;right:0;top:0;font-size:30px;opacity:0}.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .upload_space .ui_btn[data-v-13a12d32]:hover{text-decoration:none}.Datalist .dialogcontainer .createdatasetsdialog .dialog_body .ruleForm .dialog_button[data-v-13a12d32]{padding-top:16px}