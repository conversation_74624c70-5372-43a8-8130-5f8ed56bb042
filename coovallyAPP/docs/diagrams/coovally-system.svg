<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <rect x="0" y="0" width="1200" height="800" fill="#f8f9fa"/>
  <text x="600" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle" fill="#333">🚀 Coovally AI Platform 系统结构图</text>
  <rect x="450" y="80" width="300" height="50" fill="#ff6b6b" stroke="#333" stroke-width="2" rx="10"/>
  <text x="600" y="110" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#fff">Coovally AI Platform</text>
  <rect x="50" y="180" width="350" height="300" fill="#4ecdc4" stroke="#333" stroke-width="2" rx="10"/>
  <text x="225" y="210" font-family="Arial" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">💻 Web应用模块</text>
  <rect x="80" y="240" width="240" height="80" fill="#fff3cd" stroke="#007bff" stroke-width="2" rx="8"/>
  <text x="200" y="265" font-family="Arial" font-size="12" text-anchor="middle" fill="#007bff">🎨 图像增强系统（核心功能）</text>
  <text x="200" y="285" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">预处理：色调曲线 伽马校正 锐化增强</text>
  <text x="200" y="300" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">增强效果：雾化 雨滴 阴影 几何变换</text>
  <rect x="425" y="180" width="350" height="200" fill="#45b7d1" stroke="#333" stroke-width="2" rx="10"/>
  <text x="600" y="210" font-family="Arial" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">🖥️ 桌面应用模块</text>
  <text x="600" y="260" font-family="Arial" font-size="12" text-anchor="middle" fill="#fff">Electron + Vue.js + 原生功能集成</text>
  <text x="600" y="280" font-family="Arial" font-size="10" text-anchor="middle" fill="#fff">跨平台支持：Windows | macOS | Linux</text>
  <rect x="800" y="180" width="350" height="200" fill="#96ceb4" stroke="#333" stroke-width="2" rx="10"/>
  <text x="975" y="210" font-family="Arial" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">🌐 云服务模块</text>
  <text x="975" y="260" font-family="Arial" font-size="12" text-anchor="middle" fill="#fff">RaaS服务 + API网关 + 云存储 + GPU算力</text>
  <rect x="425" y="420" width="350" height="60" fill="#ffeaa7" stroke="#333" stroke-width="2" rx="10"/>
  <text x="600" y="445" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">🌟 平台特性</text>
  <text x="600" y="465" font-family="Arial" font-size="11" text-anchor="middle" fill="#333">🎯 AI驱动 | ☁️ 云端部署 | 📱 跨平台 | 🔧 可扩展</text>
</svg>
