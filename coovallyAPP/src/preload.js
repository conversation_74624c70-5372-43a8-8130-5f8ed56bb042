const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// 暴露保护的方法给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
    // 平台信息
    platform: process.platform,
    
    // 版本信息
    versions: {
        node: process.versions.node,
        chrome: process.versions.chrome,
        electron: process.versions.electron
    },
    
    // 菜单事件监听
    onMenuAction: (callback) => ipcRenderer.on('menu-new', callback),
    onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),
    
    // 移除事件监听器
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
    
    // 桌面特定功能
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    
    // 文件系统访问（安全的）
    readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
    writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
    
    // 应用控制
    minimize: () => ipcRenderer.send('window-minimize'),
    maximize: () => ipcRenderer.send('window-maximize'),
    close: () => ipcRenderer.send('window-close'),
    
    // 主题检测
    isDarkTheme: () => ipcRenderer.invoke('is-dark-theme'),
    onThemeChanged: (callback) => ipcRenderer.on('theme-changed', callback),
    
    // 网络状态
    isOnline: () => navigator.onLine,
    onOnline: (callback) => window.addEventListener('online', callback),
    onOffline: (callback) => window.addEventListener('offline', callback),
    
    // 桌面通知
    showNotification: (title, options) => {
        if (Notification.permission === 'granted') {
            return new Notification(title, options);
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    return new Notification(title, options);
                }
            });
        }
    },
    
    // 开发模式检测
    isDev: process.env.NODE_ENV === 'development'
});

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    // 添加桌面应用标识
    document.body.classList.add('electron-app');
    
    // 设置平台特定的CSS类
    document.body.classList.add(`platform-${process.platform}`);
    
    // 处理拖拽文件（可选功能）
    document.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.stopPropagation();
    });
    
    document.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        // 可以在这里处理文件拖拽
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            // 发送文件信息到渲染进程
            window.dispatchEvent(new CustomEvent('files-dropped', { detail: files }));
        }
    });
});

// 禁用某些快捷键（可选）
document.addEventListener('keydown', (e) => {
    // 禁用F12（开发者工具）在生产环境
    if (e.key === 'F12' && !process.env.NODE_ENV === 'development') {
        e.preventDefault();
    }
    
    // 禁用Ctrl+Shift+I（开发者工具）在生产环境
    if (e.ctrlKey && e.shiftKey && e.key === 'I' && !process.env.NODE_ENV === 'development') {
        e.preventDefault();
    }
});

// 错误处理
window.addEventListener('error', (e) => {
    console.error('Renderer Error:', e);
    // 可以发送错误信息到主进程进行日志记录
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled Promise Rejection:', e);
    // 可以发送错误信息到主进程进行日志记录
}); 